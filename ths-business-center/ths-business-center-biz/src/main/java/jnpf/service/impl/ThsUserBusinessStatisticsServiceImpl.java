package jnpf.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import jnpf.entity.ThsBusinessBudgetProjectDetailEntity;
import jnpf.entity.ThsUserExtensionInformationEntity;
import jnpf.model.statistics.ThsUserBusinessStatisticsModel;
import jnpf.model.statistics.ThsUserBusinessStatisticsOtherFeeModel;
import jnpf.model.statistics.ThsUserBusinessStatisticsPagination;
import jnpf.permission.entity.OrganizeEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.OrganizeService;
import jnpf.permission.service.UserService;
import jnpf.service.*;
import jnpf.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther wxy
 * @date 2024-03-28 15:02
 */
@Service
public class ThsUserBusinessStatisticsServiceImpl implements ThsUserBusinessStatisticsService {

    private final Logger log = LoggerFactory.getLogger(ThsUserFeatServiceImpl.class);

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectDetailService thsBusinessBudgetProjectDetailService;


    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectFlowDetailsService thsBusinessBudgetProjectFlowDetailsService;

    @Autowired
    @Lazy
    private ThsSysDictDetailsService thsSysDictDetailsService;


    @Autowired
    private UserService userApi;

    @Autowired
    private OrganizeService organizeApi;

    @Autowired
    private ThsUserExtensionInformationService thsUserExtensionInformationService;

    /**
     * @description: 查询统计的基础数据
     * @param: @param thsUserBusinessStatisticsPagination
     * @return: java.util.List<jnpf.model.performance.ThsProjectCostDetailModel>
     * @author: wxy
     * @date: 2024/3/28 15:04
     */
    @Override
    public List<Map<String, Object>> getProjectCostDetailList(ThsUserBusinessStatisticsPagination thsUserBusinessStatisticsPagination) {

        // 分类查询统计
        List<Map<String, Object>> resultMap = new ArrayList<>();
        // 查询单位工程基础的造价数据
        List<ThsUserBusinessStatisticsModel> tmpProjectUnitCostList = this.getUnitCostBasicData(thsUserBusinessStatisticsPagination);
        if (CollUtil.isEmpty(tmpProjectUnitCostList)) {
            return CollUtil.newArrayList(); // 返回空列表而不是null
        }
        // 计算单位工程的所有人均造价
        tmpProjectUnitCostList.forEach(model -> {
            Integer userCount = ObjectUtil.defaultIfNull(model.getUserCount(), 0);
            if (userCount > 0) {
                // 安全计算人均价格
                model.setSendTotalPrice(ObjectUtil.defaultIfNull(model.getSendTotalPrice(), 0.0) / userCount);
                model.setTotalPrice(ObjectUtil.defaultIfNull(model.getTotalPrice(), 0.0) / userCount);
                model.setDeductionAmount(ObjectUtil.defaultIfNull(model.getDeductionAmount(), 0.0) / userCount);
                model.setGrantAmount(ObjectUtil.defaultIfNull(model.getGrantAmount(), 0.0) / userCount);
            } else {
                // 用户数量为0时，设置为0.0
                model.setSendTotalPrice(0.0);
                model.setTotalPrice(0.0);
                model.setDeductionAmount(0.0);
                model.setGrantAmount(0.0);
            }
        });
        // 计算所有单位工程的人均笔数
        List<ThsUserBusinessStatisticsModel> averageUnitCostList = new ArrayList<>();
        for (ThsUserBusinessStatisticsModel unitCost : tmpProjectUnitCostList) {
            boolean flag = false;
            String a1 = unitCost.getBudgetProjectId() + "_" + unitCost.getPid() + "_" + unitCost.getUserId();
            for (ThsUserBusinessStatisticsModel groupUnitCost : averageUnitCostList) {
                String a2 = groupUnitCost.getBudgetProjectId() + "_" + groupUnitCost.getPid() + "_" + groupUnitCost.getUserId();
                if (a1.equals(a2)) {
                    // 使用工具方法累加统计数据
                    accumulateStatisticsModel(groupUnitCost, unitCost);
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                averageUnitCostList.add(unitCost);
            }
        }
        //  获取单项工程数据
        List<String> dxIds = averageUnitCostList.stream().map(ThsUserBusinessStatisticsModel::getPid).collect(Collectors.toList());
        List<ThsUserBusinessStatisticsModel> singleCostList = thsBusinessBudgetProjectDetailService.getCostListById(dxIds);

        // 单项工程的人均造价
        for (ThsUserBusinessStatisticsModel unitCost : averageUnitCostList) {
            singleCostList.stream().filter(t -> t.getPid().equals(unitCost.getId())).findFirst().ifPresent(findaverageUnitCost -> copyBasicFields(unitCost, findaverageUnitCost));
        }
        // 单项工程人均笔数
        // 计算所有单项的人均笔数
        List<ThsUserBusinessStatisticsModel> averageSingleCostList = new ArrayList<>();
        for (ThsUserBusinessStatisticsModel averageUnitCostModel : averageUnitCostList) {
            boolean flag = false;
            String a1 = averageUnitCostModel.getBudgetProjectId() + "_" + averageUnitCostModel.getPid() + "_" + averageUnitCostModel.getUserId();
            for (ThsUserBusinessStatisticsModel groupUnitCost : averageSingleCostList) {
                String a2 = groupUnitCost.getBudgetProjectId() + "_" + groupUnitCost.getPid() + "_" + groupUnitCost.getUserId();
                if (a1.equals(a2)) {
                    // 使用工具方法累加统计数据
                    accumulateStatisticsModel(groupUnitCost, averageUnitCostModel);
                    // 设置其他字段
                    groupUnitCost.setConsultingTypeId(ObjectUtil.defaultIfNull(averageUnitCostModel.getConsultingTypeId(), ""));
                    groupUnitCost.setEditorialTypeId(ObjectUtil.defaultIfNull(averageUnitCostModel.getEditorialTypeId(), ""));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                averageSingleCostList.add(averageUnitCostModel);
            }
        }
        // 计算所有建设项目的人均造价

        //  获取建设项目工程数据
        List<String> projectJSIds = averageSingleCostList.stream().map(ThsUserBusinessStatisticsModel::getPid).collect(Collectors.toList());
        List<ThsUserBusinessStatisticsModel> JSCostList = thsBusinessBudgetProjectDetailService.getCostListById(projectJSIds);

        // 建设项目的人均工程造价
        for (ThsUserBusinessStatisticsModel unitCost : averageSingleCostList) {
            ThsUserBusinessStatisticsModel findaverageUnitCost = JSCostList.stream().filter(t -> t.getPid().equals(unitCost.getId())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(findaverageUnitCost)) {
                // 使用工具方法复制基本字段
                copyBasicFields(unitCost, findaverageUnitCost);
            }
        }

        // 计算所有建设项目的人均笔数
        List<ThsUserBusinessStatisticsModel> userPrjJSList = new ArrayList<>();

        for (ThsUserBusinessStatisticsModel averageSingleCostModel : averageSingleCostList) {
            boolean flag = false;
            String a1 = averageSingleCostModel.getBudgetProjectId() + "_" + averageSingleCostModel.getUserId();
            for (ThsUserBusinessStatisticsModel groupUnitCost : userPrjJSList) {
                String a2 = groupUnitCost.getBudgetProjectId() + "_" + groupUnitCost.getUserId();
                if (a1.equals(a2)) {
                    // 使用工具方法累加统计数据
                    accumulateStatisticsModel(groupUnitCost, averageSingleCostModel);

                    // 设置其他字段
                    groupUnitCost.setConsultingTypeId(ObjectUtil.defaultIfNull(averageSingleCostModel.getConsultingTypeId(), ""));
                    groupUnitCost.setEditorialTypeId(ObjectUtil.defaultIfNull(averageSingleCostModel.getEditorialTypeId(), ""));

                    flag = true;
                    break;
                }
            }
            if (!flag) {
                userPrjJSList.add(averageSingleCostModel);
            }
        }

        // 获取单项层其他费,获取项目层其他费
        Integer status = null;
        if (ObjectUtil.isNotNull(thsUserBusinessStatisticsPagination.getUseIsFinished())) {
            // 判断
            status = BooleanUtil.isTrue(thsUserBusinessStatisticsPagination.isFinished()) ? 3 : -1;
        }
        List<ThsUserBusinessStatisticsModel> tmpOtherFeeList = thsBusinessBudgetProjectDetailService.getOtherFeeListByDetailIds(status, dxIds);

        // 根据项目ID和userID，汇总其他费

        List<ThsUserBusinessStatisticsModel> allOtherFeeList = new ArrayList<>();
        for (ThsUserBusinessStatisticsModel costModel : tmpOtherFeeList) {
            boolean flag = false;
            String a1 = costModel.getBudgetProjectId() + "_" + costModel.getUserId();
            for (ThsUserBusinessStatisticsModel groupUnitCost : allOtherFeeList) {
                String a2 = groupUnitCost.getBudgetProjectId() + "_" + groupUnitCost.getUserId();
                if (a1.equals(a2)) {
                    // 使用工具方法累加统计数据
                    accumulateStatisticsModel(groupUnitCost, costModel);

                    // 根据项目ID获取合同类型咨询项目类型编审类型数据
                    ThsUserBusinessStatisticsModel findUnitModel = averageUnitCostList.stream()
                            .filter(t -> t.getBudgetProjectId().equals(groupUnitCost.getBudgetProjectId()))
                            .findFirst().orElse(null);

                    if (ObjectUtil.isNotNull(findUnitModel)) {
                        // 设置字段
                        groupUnitCost.setConsultingTypeId(ObjectUtil.defaultIfNull(findUnitModel.getConsultingTypeId(), ""));
                        groupUnitCost.setEditorialTypeId(ObjectUtil.defaultIfNull(findUnitModel.getEditorialTypeId(), ""));
                        groupUnitCost.setContractCode(ObjectUtil.defaultIfNull(findUnitModel.getContractCode(), ""));
                    }
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                allOtherFeeList.add(costModel);
            }
        }

        List<ThsUserBusinessStatisticsModel> allProjectList = new ArrayList<>();

        //  项目费用合并
        userPrjJSList.addAll(allOtherFeeList);

        // 根据 用户ID，编审类型和合同编号和咨询类型分组统计，

        for (ThsUserBusinessStatisticsModel costModel : userPrjJSList) {
            boolean flag = false;
            String a1 = costModel.getUserId() + "_" + costModel.getEditorialTypeId() + "_" + costModel.getContractCode() + "_" + costModel.getConsultingTypeId();
            for (ThsUserBusinessStatisticsModel groupUnitCost : allProjectList) {
                String a2 = groupUnitCost.getBudgetProjectId() + "_" + groupUnitCost.getUserId();
                if (a1.equals(a2)) {
                    // 使用工具方法累加统计数据
                    accumulateStatisticsModel(groupUnitCost, costModel);
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                allProjectList.add(costModel);
            }
        }
        //
        // 咨询类型字典数据
        List<Map<String, Object>> consultingTypeMapList = thsSysDictDetailsService.getListTree("QJ-ZXLX", false);
        List<Map<String, Object>> editorialTypeMapList = thsSysDictDetailsService.getListTree("QJ-BSLX", false);

        List<UserEntity> userEntityList = userApi.getList(false);
        List<OrganizeEntity> organizeEntityList = organizeApi.getList(false);

        Map<String, Map<String, Object>> groupSumMap = new HashMap<>();

        // 遍历 allProjectList 进行分组统计
        for (ThsUserBusinessStatisticsModel model : allProjectList) {
            String groupKey = model.getUserId() + "_" + model.getEditorialTypeId() + "_" + model.getConsultingTypeId() + "_" + model.getContractCode(); // 使用组合的属性作为键

            if (!groupSumMap.containsKey(groupKey)) {
                Map<String, Object> innerMap = new HashMap<>();
                innerMap.put("model", model);
                groupSumMap.put(groupKey, innerMap);
                //  根据编审类型汇总价格数据
                this.sumUpCostDataByEditorialType(model, innerMap, editorialTypeMapList, consultingTypeMapList);
                // 人员名称
                // 用户ID判空
                if (StrUtil.isBlank(model.getUserId())) {
                    innerMap.put("userName", "未指定项目经理");
                    innerMap.put("OrganizeName", "未指定项目经理");
                } else {
                    // 查找用户信息
                    Optional<UserEntity> userInfo = userEntityList.stream().filter(user -> user.getId().equals(model.getUserId())).findFirst();

                    if (userInfo.isPresent()) {
                        UserEntity user = userInfo.get();
                        innerMap.put("userName", user.getRealName());

                        // 获取专业信息
                        ThsUserExtensionInformationEntity entity = thsUserExtensionInformationService.getEntityByUserId(user.getId());
                        if (ObjectUtil.isNotNull(entity)) {
                            innerMap.put("specialtyName", StrUtil.blankToDefault(entity.getFspecialtyName(), ""));
                        }

                        // 获取部门信息
                        Optional<OrganizeEntity> organizeInfo = organizeEntityList.stream().filter(organize -> organize.getId().equals(user.getOrganizeId())).findFirst();
                        organizeInfo.ifPresent(organize -> innerMap.put("OrganizeName", organize.getFullName()));
                    } else {
                        // 用户不存在的处理逻辑
                        innerMap.put("userName", "人员不存在");
                        innerMap.put("OrganizeName", "人员不存在");
                    }
                }
            } else {
                // 如果分组已经存在，则将当前行的属性进行汇总
                Map<String, Object> innerMap = groupSumMap.get(groupKey);
                this.sumUpCostDataByEditorialType(model, innerMap, editorialTypeMapList, consultingTypeMapList);
            }
        }

        // 按照用户ID业务统计汇总
        for (Map.Entry<String, Map<String, Object>> mapEntry : groupSumMap.entrySet()) {
            String outerKey = mapEntry.getKey();
            Map<String, Object> innerMap = mapEntry.getValue();

            ThsUserBusinessStatisticsModel userBusinessStatisticsModel = null;
            if (innerMap.containsKey("model")) {
                userBusinessStatisticsModel = (ThsUserBusinessStatisticsModel) innerMap.get("model");
            }
            String userId = userBusinessStatisticsModel.getUserId();
            // 安全比较用户ID
            Map<String, Object> existingMap = resultMap.stream()
                    .filter(map -> ObjectUtil.equal(map.get("userId"), userId))
                    .findFirst()
                    .orElse(null);

            // 不存在用户，新建Map
            if (existingMap == null) {
                // Create a new map if no existing entry found
                Map<String, Object> newMap = new HashMap<>();
                newMap.put("id", RandomUtil.uuId());
                newMap.put("groupKey", outerKey);
                Map<String, Object> modelMap = JsonUtil.entityToMap(userBusinessStatisticsModel);
                newMap.putAll(modelMap);

                // 复制内部数据（跳过model字段）
                for (Map.Entry<String, Object> innerEntry : innerMap.entrySet()) {
                    if (!"model".equals(innerEntry.getKey())) {
                        newMap.put(innerEntry.getKey(), innerEntry.getValue());
                    }
                }

                // 使用工具方法安全计算总计数据
                Integer zjRecordCount = safeGetInteger(innerMap, "BZ_RecordCount") + safeGetInteger(innerMap, "SH_RecordCount");
                Integer zjXMCount = safeGetInteger(innerMap, "BZ_XMCount") + safeGetInteger(innerMap, "SH_XMCount");
                Double zjTotalMoney = safeGetDouble(innerMap, "BZ_TotalMoney") + safeGetDouble(innerMap, "SH_TotalMoney");

                // 设置总计数据
                newMap.put("ZJ_RecordCount", zjRecordCount);
                newMap.put("ZJ_XMCount", zjXMCount);
                newMap.put("ZJ_TotalMoney", NumberUtil.round(zjTotalMoney, 2).toString());

                resultMap.add(newMap);
            } else {
                // 合并已存在的数据
                for (Map.Entry<String, Object> innerEntry : innerMap.entrySet()) {
                    if (!"model".equals(innerEntry.getKey())) {
                        if (innerEntry.getValue() instanceof Number) {
                            existingMap.merge(innerEntry.getKey(), innerEntry.getValue(), (oldVal, newVal) -> {
                                if (oldVal instanceof Number) {
                                    return ((Number) oldVal).doubleValue() + ((Number) newVal).doubleValue();
                                }
                                return newVal;
                            });
                        } else {
                            existingMap.put(innerEntry.getKey(), innerEntry.getValue());
                        }
                    }
                }

                // 使用工具方法安全计算累加的总计数据
                Integer zjRecordCount = safeGetInteger(innerMap, "BZ_RecordCount") + safeGetInteger(innerMap, "SH_RecordCount") + safeGetInteger(existingMap, "ZJ_RecordCount");
                Integer zjXMCount = safeGetInteger(innerMap, "BZ_XMCount") + safeGetInteger(innerMap, "SH_XMCount") + safeGetInteger(existingMap, "ZJ_XMCount");
                Double zjTotalMoney = safeGetDouble(innerMap, "BZ_TotalMoney") + safeGetDouble(innerMap, "SH_TotalMoney") + safeGetDouble(existingMap, "ZJ_TotalMoney");

                // 更新总计数据
                existingMap.put("ZJ_RecordCount", zjRecordCount);
                existingMap.put("ZJ_XMCount", zjXMCount);
                existingMap.put("ZJ_TotalMoney", NumberUtil.round(zjTotalMoney, 2).toString());
            }
        }

        // 合计
        Map<String, Object> totalRow = new HashMap<>();
        totalRow.put("id", "total");
        totalRow.put("userName", "合计");
        totalRow.put("OrganizeName", "合计");

        // 优化数值累加逻辑
        for (Map<String, Object> map : resultMap) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() instanceof Number) {
                    String key = entry.getKey();
                    double value = ((Number) entry.getValue()).doubleValue();

                    // 安全累加数值
                    double currentTotal = ObjectUtil.defaultIfNull((Double) totalRow.get(key), 0.0);
                    totalRow.put(key, NumberUtil.add(currentTotal, value));
                }
            }
        }

        // 格式化总和（只格式化数值类型）
        for (Map.Entry<String, Object> entry : totalRow.entrySet()) {
            if (entry.getValue() instanceof Number) {
                double numValue = ((Number) entry.getValue()).doubleValue();
                entry.setValue(NumberUtil.round(numValue, 2).toString());
            }
        }
        // 将合计行添加到resultMap中
        resultMap.add(totalRow);

        return resultMap;
    }

    /**
     * @description: 根据筛选条件查询单位工程造价基础数据
     * @param: @param thsUserBusinessStatisticsPagination
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 9:55
     */
    @Override
    public List<ThsUserBusinessStatisticsModel> getUnitCostBasicData(ThsUserBusinessStatisticsPagination thsUserBusinessStatisticsPagination) {
        // 初始化查询条件
        // 过滤合同数据
        List<ThsUserBusinessStatisticsModel> resultList = null;
        String contractCode = thsUserBusinessStatisticsPagination.getContractCode() != null ? thsUserBusinessStatisticsPagination.getContractCode().toString() : null;

        String signingBeginDate = thsUserBusinessStatisticsPagination.getSigningBeginDate() != null ? thsUserBusinessStatisticsPagination.getSigningBeginDate().toString() : null;
        String signingEndDate = thsUserBusinessStatisticsPagination.getSigningEndDate() != null ? thsUserBusinessStatisticsPagination.getSigningEndDate().toString() : null;

        // 修复状态筛选逻辑：根据isFinished字段来控制查询已完成还是未完成
        // 已完成是status=3，未完成是status!=3
        Integer status = null;
        if (thsUserBusinessStatisticsPagination.getUseIsFinished() != null) {
            // 如果前端传了筛选标识，则根据isFinished字段来决定查询条件
            status = thsUserBusinessStatisticsPagination.isFinished() ? 3 : -1; // -1表示查询非3的状态
        }
        String finishBeginTime = thsUserBusinessStatisticsPagination.getFinishBeginTime() != null ? thsUserBusinessStatisticsPagination.getFinishBeginTime().toString() : null;
        String finishEndTime = thsUserBusinessStatisticsPagination.getFinishEndTime() != null ? thsUserBusinessStatisticsPagination.getFinishEndTime().toString() : null;
        List<String> businessTypeIdList = null;
        if (ObjectUtil.isNotEmpty(thsUserBusinessStatisticsPagination.getBusinessTypeId())) {
            businessTypeIdList = (List<String>) thsUserBusinessStatisticsPagination.getBusinessTypeId();
        }
        List<String> userIds = null;
        if (ObjectUtil.isNotEmpty(thsUserBusinessStatisticsPagination.getUserID())) {
            userIds = (List<String>) thsUserBusinessStatisticsPagination.getUserID();
        }
        List<String> editorialTypeIds = null;
        if (ObjectUtil.isNotEmpty(thsUserBusinessStatisticsPagination.getUseEditorialType()) && ObjectUtil.isNotEmpty(thsUserBusinessStatisticsPagination.getEditorialType())) {

            editorialTypeIds = new ArrayList<>();
            List<Map<String, Object>> editorialTypeMapList = thsSysDictDetailsService.getListTree("QJ-BSLX", false);
            if (ObjectUtil.isEmpty(editorialTypeMapList) || (editorialTypeMapList.size() == 0)) {
                log.error("字典中未配置编审类型");
                return null;
            }
            for (Map<String, Object> map : editorialTypeMapList) {
                String categoryId = map.get("id").toString();
                String itemText = map.get("itemText").toString();
                String itemValue = map.get("itemValue").toString();
                if (thsUserBusinessStatisticsPagination.getEditorialType().equals("审核") &&
                        StringUtil.isNotEmpty(itemText) && (
                        itemText.equals(thsUserBusinessStatisticsPagination.getEditorialType())
                                || (StringUtil.isNotEmpty(itemValue) && itemValue.equals("AUDIT_PROJECT")))) {
                    editorialTypeIds.add(categoryId);
                }
                if (thsUserBusinessStatisticsPagination.getEditorialType().equals("编制") &&
                        StringUtil.isNotEmpty(itemText) && itemText.equals(thsUserBusinessStatisticsPagination.getEditorialType())
                ) {
                    editorialTypeIds.add(categoryId);
                }

            }
            thsUserBusinessStatisticsPagination.setEditorialTypeIds(editorialTypeIds);
        }

        long startTime = System.currentTimeMillis();
        // 根据编审中不同的角色类型，查询单位工程基础的造价数据
        resultList = thsBusinessBudgetProjectDetailService.getCostListByDynamic(contractCode,
                signingBeginDate,
                signingEndDate,
                status,
                finishBeginTime,
                finishEndTime,
                editorialTypeIds,
                businessTypeIdList,
                userIds,
                thsUserBusinessStatisticsPagination.getRoleType().toString());

        long endTime = System.currentTimeMillis();
        // 计算方法执行时间
        long duration = endTime - startTime;
        log.info("单位工程造价查询时长" + duration);
        return resultList;
    }

    /**
     * @param innerMap              汇总的Map
     * @param consultingTypeMapList 咨询项目类型字典列表
     * @description: 根据编审类型汇总价格数据
     * @param: @param currentModel 当前遍历的数据
     * @return: void
     * @author: wxy
     * @date: 2024/4/18 19:27
     */
    @Override
    public void sumUpCostDataByEditorialType(ThsUserBusinessStatisticsModel currentModel, Map<String, Object> innerMap, List<Map<String, Object>> editorialTypeMapList, List<Map<String, Object>> consultingTypeMapList) {
        // 安全获取现有模型
        ThsUserBusinessStatisticsModel existingModel = (ThsUserBusinessStatisticsModel) innerMap.get("model");
        if (ObjectUtil.isNull(existingModel) || StrUtil.isBlank(existingModel.getEditorialTypeId())) {
            return;
        }

        String editorialTypeString = thsBusinessBudgetProjectDetailService.checkEditorialType(editorialTypeMapList, existingModel.getEditorialTypeId());

        // 初始化审核金额汇总列
        initializeConsultingTypeKeys(innerMap, consultingTypeMapList, "SH");
        // 初始化编制金额汇总列
        initializeConsultingTypeKeys(innerMap, consultingTypeMapList, "BZ");

        // 优化编制类型处理
        if (StrUtil.equals(editorialTypeString, "编制")) {
            processBzType(innerMap, currentModel, consultingTypeMapList);
        } else if (StrUtil.equals(editorialTypeString, "审核")) {
            processShType(innerMap, currentModel, consultingTypeMapList);
        }
    }


    /**
     * @description: 个人业务情况查询，根据咨询类型和编审类型获取动态列
     * @param:
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @author: wxy
     * @date: 2024/4/19 13:44
     */
    @Override
    public Map<String, Object> getUserBusinessStatisticsColumns() {
        Map<String, Object> resultMap = new HashMap<>();
        // 咨询类型字典数据
        List<Map<String, Object>> consultingTypeMapList = thsSysDictDetailsService.getListTree("QJ-ZXLX", false);
        List<Map<String, Object>> editorialTypeMapList = thsSysDictDetailsService.getListTree("QJ-BSLX", false);
        if (ObjectUtil.isEmpty(consultingTypeMapList) || (consultingTypeMapList.size() == 0)) {
            log.error("字典中位配置咨询项目类型");
        }
        if (ObjectUtil.isEmpty(editorialTypeMapList) || (consultingTypeMapList.size() == 0)) {
            log.error("字典中未配置编审类型");
        }
        // 根据编审类型和咨询类型汇总金额
        Map<String, Object> bzMap = new HashMap<>();
        for (Map<String, Object> map : consultingTypeMapList) {
            String categoryId = map.get("id").toString();
            String itemText = map.get("itemText").toString();
            String itemCode = map.get("itemCode").toString();
            String ConsultingTypekeyName = itemCode + "_" + "BZ_" + "TotalMoney";
            bzMap.put(ConsultingTypekeyName, itemText);
        }
        // 审核金额汇总列
        Map<String, Object> shMap = new HashMap<>();
        for (Map<String, Object> map : consultingTypeMapList) {
            String categoryId = map.get("id").toString();
            String itemText = map.get("itemText").toString();
            String itemCode = map.get("itemCode").toString();
            String ConsultingTypekeyName = itemCode + "_" + "SH_" + "TotalMoney";
            shMap.put(ConsultingTypekeyName, itemText);
        }
        resultMap.put("BZ", bzMap);
        resultMap.put("SH", shMap);
        return resultMap;
    }

    /**
     * @description: 个人业务情况统计查看项目明细
     * @param: @param thsUserBusinessStatisticsPagination
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @author: wxy
     * @date: 2024/4/20 15:50
     */
    @Override
    public List<Map<String, Object>> getProjectDetail(ThsUserBusinessStatisticsPagination thsUserBusinessStatisticsPagination) {
        // 汇总結果数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 此处的基础数据查询的编制人的单位工程基础数据
        // thsUserBusinessStatisticsPagination.setRoleType("1");
        thsUserBusinessStatisticsPagination.setUseEditorialType(true);

        try {
            List<ThsUserBusinessStatisticsModel> tmpProjectUnitCostList = this.getUnitCostBasicData(thsUserBusinessStatisticsPagination);
            if (ObjectUtil.isEmpty(tmpProjectUnitCostList)) {
                return new ArrayList<>(); // 返回空列表而不是null，避免调用方NPE
            }
            //  获取单项工程数据
            List<String> dxIdList = tmpProjectUnitCostList.stream()
                    .map(ThsUserBusinessStatisticsModel::getPid)
                    .filter(pid -> pid != null && !pid.isEmpty())
                    .collect(Collectors.toList());
            List<String> editorialTypeIds = null;
            if (ObjectUtil.isNotEmpty(thsUserBusinessStatisticsPagination.getUseEditorialType())) {
                // 安全的类型转换
                Object editorialTypeIdsObj = thsUserBusinessStatisticsPagination.getEditorialTypeIds();
                if (editorialTypeIdsObj instanceof List) {
                    editorialTypeIds = (List<String>) editorialTypeIdsObj;
                }
            }
            // 查询其他费数据
            String dxIdsString = StringUtils.convertListToString(dxIdList);
            String editorialTypeIdsString = CollUtil.isNotEmpty(editorialTypeIds) ? StringUtils.convertListToString(editorialTypeIds) : "null";

            List<ThsUserBusinessStatisticsOtherFeeModel> tmpOtherFeeSingleList = thsBusinessBudgetProjectDetailService.selectOtherFeeSingleListByDetailIdsAndEditorialTypeId(dxIdsString, editorialTypeIdsString);
            // 根据项目ID和 PID和userID，汇总费用
            List<ThsUserBusinessStatisticsOtherFeeModel> allOtherSingleList = new ArrayList<>();
            for (ThsUserBusinessStatisticsOtherFeeModel otherFeeModel : tmpOtherFeeSingleList) {
                // 安全的类型转换
                Object userIdObj = thsUserBusinessStatisticsPagination.getUserID();
                if (!(userIdObj instanceof List)) {
                    continue;
                }
                List<String> userIds = (List<String>) userIdObj;
                if (CollUtil.isEmpty(userIds) || !userIds.contains(otherFeeModel.getUserId())) {
                    continue;
                }
                boolean flag = false;
                String a1 = otherFeeModel.getBudgetProjectId() + "_" + otherFeeModel.getId() + "_" + otherFeeModel.getPid() + "_" + otherFeeModel.getUserId();
                for (ThsUserBusinessStatisticsOtherFeeModel groupOtherFeeModel : allOtherSingleList) {
                    String a2 = groupOtherFeeModel.getBudgetProjectId() + "_" + groupOtherFeeModel.getId() + "_" + groupOtherFeeModel.getPid() + "_" + groupOtherFeeModel.getUserId();
                    if (a1.equals(a2)) {
                        // 累加总价和发送总价
                        groupOtherFeeModel.setTotalPrice(
                                ObjectUtil.defaultIfNull(groupOtherFeeModel.getTotalPrice(), 0.0) +
                                        ObjectUtil.defaultIfNull(otherFeeModel.getTotalPrice(), 0.0)
                        );
                        groupOtherFeeModel.setSendTotalPrice(
                                ObjectUtil.defaultIfNull(groupOtherFeeModel.getSendTotalPrice(), 0.0) +
                                        ObjectUtil.defaultIfNull(otherFeeModel.getSendTotalPrice(), 0.0)
                        );

                        // 如果已完成，累加完成相关的价格
                        if (otherFeeModel.getFinished()) {
                            groupOtherFeeModel.setFinishTotalPriceEx(
                                    ObjectUtil.defaultIfNull(groupOtherFeeModel.getFinishTotalPriceEx(), 0.0) +
                                            ObjectUtil.defaultIfNull(otherFeeModel.getTotalPrice(), 0.0)
                            );
                            groupOtherFeeModel.setFinishSendTotalPriceEx(
                                    ObjectUtil.defaultIfNull(groupOtherFeeModel.getFinishSendTotalPriceEx(), 0.0) +
                                            ObjectUtil.defaultIfNull(otherFeeModel.getSendTotalPrice(), 0.0)
                            );
                        }
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    allOtherSingleList.add(otherFeeModel);
                }
            }
            // 根据单位工程数据递归查找所有项目
            List<ThsUserBusinessStatisticsModel> allProjectDetailList = new ArrayList<>(tmpProjectUnitCostList);
            //  递归添加项目详细信息
            recursivelyAddProjectDetails(allProjectDetailList, dxIdList, 0);

            // 过滤当前用户的项目IDs
            List<String> currentUserDetailIdList = allProjectDetailList.stream().map(ThsUserBusinessStatisticsModel::getId).collect(Collectors.toList());

            String currentUserDetailIds = StringUtils.convertListToString(currentUserDetailIdList);

            if (!currentUserDetailIds.isEmpty()) {
                resultList = thsBusinessBudgetProjectDetailService.getAllProjectDetailList(currentUserDetailIds);
            }

            //  根据工程Ids获取审核人员数据
            List<Map<String, Object>> flowUserList = thsBusinessBudgetProjectFlowDetailsService.findFlowDetailUserList(currentUserDetailIds);

            //  获取字典
            List<Map<String, Object>> consultingTypeMapList = thsSysDictDetailsService.getListTree("QJ-ZXLX", false);

            List<UserEntity> userEntityList = userApi.getList(false);
            // 遍历填充其他费和其他数据
            for (Map<String, Object> map : resultList) {
                // 流程人
                if (ObjectUtil.isNotEmpty(flowUserList)) {

                    List<String> JHRY = flowUserList.stream()
                            .filter(flowMap -> flowMap.containsKey("detail_id") &&
                                    flowMap.get("detail_id").equals(map.get("id")) &&
                                    flowMap.containsKey("role_type") &&
                                    flowMap.get("role_type").toString().equals("2"))
                            .map(flowMap -> (String) flowMap.get("user_name")) // 获取对应 key 的值并转换为 String 类型
                            .collect(Collectors.toList());
                    map.put("JHRY", String.join(",", JHRY));

                    List<String> HZRY = flowUserList.stream()
                            .filter(flowMap -> flowMap.containsKey("detail_id") &&
                                    flowMap.get("detail_id").equals(map.get("id")) &&
                                    flowMap.containsKey("role_type") &&
                                    flowMap.get("role_type").toString().equals("3"))
                            .map(flowMap -> (String) flowMap.get("user_name")) // 获取对应 key 的值并转换为 String 类型
                            .collect(Collectors.toList());
                    map.put("HZRY", String.join(",", HZRY));

                }

                // 其他费数据
                ThsUserBusinessStatisticsOtherFeeModel findOtherFeeModel = allOtherSingleList.stream().filter(otherFeeModel -> otherFeeModel.getId().equals(map.get("id"))).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(findOtherFeeModel) && (findOtherFeeModel != null)) {
                    map.put("OtherTotalPriceEx", findOtherFeeModel.getTotalPriceEx());
                    map.put("OtherSendTotalPriceEx", findOtherFeeModel.getSendTotalPriceEx());
                    map.put("OtherFinishTotalPriceEx", findOtherFeeModel.getFinishTotalPriceEx());
                    map.put("OtherFinishSendTotalPriceEx", findOtherFeeModel.getFinishSendTotalPriceEx());
                }

                ThsUserBusinessStatisticsModel findModel = allProjectDetailList.stream().filter(otherFeeModel -> otherFeeModel.getId().equals(map.get("id"))).findFirst().orElse(null);

                if (ObjectUtil.isNotEmpty(findModel) && (findModel != null)) {

                    if (ObjectUtil.isNotEmpty(findModel.getItemType()) && findModel.getItemType().equals(ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_DWGC) && findModel.getUserCount() > 0) {
                        // 安全的BigDecimal转换
                        BigDecimal totalPrice = null;
                        try {
                            Object totalPriceObj = map.get("total_price");
                            if (totalPriceObj instanceof BigDecimal) {
                                totalPrice = (BigDecimal) totalPriceObj;
                            } else if (totalPriceObj != null) {
                                totalPrice = new BigDecimal(totalPriceObj.toString());
                            }
                        } catch (Exception e) {
                            log.error("总价转换失败: {}", e.getMessage());
                            totalPrice = BigDecimal.ZERO;
                        }

                        Integer userCount = findModel.getUserCount();

                        // 计算人均总价（处理空值和除零）
                        BigDecimal perPersonTotal = BigDecimal.ZERO;
                        if (totalPrice != null && userCount != null && userCount > 0) {
                            try {
                                perPersonTotal = totalPrice.divide(BigDecimal.valueOf(userCount), 2, RoundingMode.HALF_UP);
                            } catch (ArithmeticException e) {
                                log.error("人均总价计算失败: {}", e.getMessage());
                            }
                        }

                        map.put("TotalMoneyEx", perPersonTotal);

                        // 根据状态计算已完成人均总价
                        Object statusObj = map.get("status");
                        if (statusObj != null && Objects.equals(statusObj, 3)) {
                            map.put("finishTotalMoneyEx", perPersonTotal);
                        } else {
                            map.put("finishTotalMoneyEx", BigDecimal.ZERO);
                        }
                    } else {
                        map.put("TotalMoneyEx", BigDecimal.ZERO);
                        Object statusObj = map.get("status");
                        if (statusObj != null && statusObj.equals(3)) {
                            map.put("finishTotalMoneyEx", BigDecimal.ZERO);
                        }
                    }
                    map.put("contractCode", findModel.getContractCode());
                    // 咨询类型
                    String consultingTypeIdName = MapUtils.getValueForKey(findModel.getConsultingTypeId(), consultingTypeMapList);
                    map.put("consultingTypeIdName", consultingTypeIdName);

                    Optional<UserEntity> userInfo = userEntityList.stream().filter(user -> user.getId().equals(findModel.getUserId())).findFirst();
                    userInfo.ifPresent(user -> map.put("XMJL", user.getRealName()));
                }
            }
        } catch (ClassCastException e) {
            log.error("数据类型转换异常: {}", e.getMessage(), e);
            throw new RuntimeException("数据格式错误，请检查输入参数", e);
        } catch (ArithmeticException e) {
            log.error("数学运算异常: {}", e.getMessage(), e);
            throw new RuntimeException("计算过程中出现错误", e);
        } catch (Exception e) {
            log.error("查询项目明细异常: {}", e.getMessage(), e);
            throw new RuntimeException("查询项目明细异常", e);
        }
        //  转树
        List<Map<String, Object>> easyTreesMap = TreeUtils.getEasyTreesMap(resultList, "id", "pid", "children");
        // 排序号
        SerialNumberUtil.findPid(easyTreesMap, "");
        return easyTreesMap;
    }

    /**
     * 累加两个ThsUserBusinessStatisticsModel的数值字段
     *
     * @param target 目标
     * @param source 来源
     * <AUTHOR>
     * @date 2025/7/31 16:30
     */
    private void accumulateStatisticsModel(ThsUserBusinessStatisticsModel target, ThsUserBusinessStatisticsModel source) {
        target.setTotalPrice(ObjectUtil.defaultIfNull(target.getTotalPrice(), 0.0) + ObjectUtil.defaultIfNull(source.getTotalPrice(), 0.0));
        target.setSendTotalPrice(ObjectUtil.defaultIfNull(target.getSendTotalPrice(), 0.0) + ObjectUtil.defaultIfNull(source.getSendTotalPrice(), 0.0));
        target.setDeductionAmount(ObjectUtil.defaultIfNull(target.getDeductionAmount(), 0.0) + ObjectUtil.defaultIfNull(source.getDeductionAmount(), 0.0));
        target.setGrantAmount(ObjectUtil.defaultIfNull(target.getGrantAmount(), 0.0) + ObjectUtil.defaultIfNull(source.getGrantAmount(), 0.0));
        target.setRecordCount(ObjectUtil.defaultIfNull(target.getRecordCount(), 0) + ObjectUtil.defaultIfNull(source.getRecordCount(), 0));
    }

    /**
     * 复制ThsUserBusinessStatisticsModel的基本字段（安全处理null值）
     *
     * @param target 目标
     * @param source 来源
     * <AUTHOR>
     * @date 2025/7/31 16:30
     */
    private void copyBasicFields(ThsUserBusinessStatisticsModel target, ThsUserBusinessStatisticsModel source) {
        target.setSendTotalPrice(ObjectUtil.defaultIfNull(source.getSendTotalPrice(), 0.0));
        target.setTotalPrice(ObjectUtil.defaultIfNull(source.getTotalPrice(), 0.0));
        target.setDeductionAmount(ObjectUtil.defaultIfNull(source.getDeductionAmount(), 0.0));
        target.setGrantAmount(ObjectUtil.defaultIfNull(source.getGrantAmount(), 0.0));
        target.setUserId(source.getUserId());
        target.setRecordCount(ObjectUtil.defaultIfNull(source.getRecordCount(), 0));
        target.setPid(ObjectUtil.defaultIfNull(source.getPid(), ""));
    }

    /**
     * 安全获取Map中的Integer值
     *
     * @param map 数据
     * @param key 键
     * @return {@link Integer}
     * <AUTHOR>
     * @date 2025/7/31 17:08
     */
    private Integer safeGetInteger(Map<String, Object> map, String key) {
        if (!map.containsKey(key)) {
            return 0;
        }
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value != null) {
            try {
                return Integer.valueOf(value.toString());
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    /**
     * 安全获取Map中的Double值
     *
     * @param map 数据
     * @param key 键
     * @return {@link Double}
     * <AUTHOR>
     * @date 2025/7/31 17:09
     */
    private Double safeGetDouble(Map<String, Object> map, String key) {
        if (!map.containsKey(key)) {
            return 0.0;
        }
        Object value = map.get(key);
        if (value instanceof Double) {
            return (Double) value;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        if (value != null) {
            try {
                return Double.valueOf(value.toString());
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }

    /**
     * 递归添加项目详情（带深度限制）
     */
    private static final int MAX_RECURSION_DEPTH = 50; // 最大递归深度

    /**
     * 递归添加项目详细信息
     *
     * @param allProjectDetailList 项目详情
     * @param pids                 父级ID
     * @param depth                递归深度
     * <AUTHOR>
     * @date 2025/7/31 16:30
     */
    public void recursivelyAddProjectDetails(List<ThsUserBusinessStatisticsModel> allProjectDetailList, List<String> pids, int depth) {
        // 终止条件：如果pids为空或递归深度超过限制，则不再继续递归
        if (pids == null || pids.isEmpty()) {
            return;
        }

        if (depth > MAX_RECURSION_DEPTH) {
            log.warn("递归深度超过限制: {}，停止递归以防止栈溢出", MAX_RECURSION_DEPTH);
            return;
        }

        try {
            // 获取pids对应的项目详情列表
            String pidsString = StringUtils.convertListToString(pids);
            List<ThsUserBusinessStatisticsModel> tmpList = thsBusinessBudgetProjectDetailService.getProjectDetailListByIds(pidsString);

            // 将项目详情列表添加到allProjectDetailList中
            if (ObjectUtil.isNotEmpty(tmpList)) {
                allProjectDetailList.addAll(tmpList);

                // 递归地处理每个项目详情的子项目
                List<String> pidStrings = tmpList.stream()
                        .map(ThsUserBusinessStatisticsModel::getPid)
                        .filter(pid -> pid != null && !pid.isEmpty()) // 过滤空值
                        .collect(Collectors.toList());

                if (!pidStrings.isEmpty()) {
                    recursivelyAddProjectDetails(allProjectDetailList, pidStrings, depth + 1);
                }
            }
        } catch (Exception e) {
            log.error("递归查询项目详情异常，深度: {}, 错误: {}", depth, e.getMessage(), e);
            // 不抛出异常，避免影响整个查询流程
        }
    }

    /**
     * 初始化咨询类型的键值对
     *
     * @param innerMap
     * @param consultingTypeMapList
     * @param typePrefix
     * @return
     * <AUTHOR>
     * @date 2025/7/31 17:29
     */
    private void initializeConsultingTypeKeys(Map<String, Object> innerMap, List<Map<String, Object>> consultingTypeMapList, String typePrefix) {
        for (Map<String, Object> map : consultingTypeMapList) {
            String itemCode = ObjectUtil.defaultIfNull(map.get("itemCode"), "").toString();
            String keyName = itemCode + "_" + typePrefix + "_TotalMoney";
            innerMap.put(keyName, 0.0);
        }
    }

    /**
     * 根据咨询类型累加金额
     *
     * @param innerMap
     * @param consultingTypeMapList
     * @param currentConsultingTypeId
     * @param typePrefix
     * @param amount
     * <AUTHOR>
     * @date 2025/7/31 17:29
     */
    private void accumulateConsultingTypeMoney(Map<String, Object> innerMap, List<Map<String, Object>> consultingTypeMapList, String currentConsultingTypeId, String typePrefix, Double amount) {
        for (Map<String, Object> map : consultingTypeMapList) {
            String categoryId = ObjectUtil.defaultIfNull(map.get("id"), "").toString();
            String itemCode = ObjectUtil.defaultIfNull(map.get("itemCode"), "").toString();
            String keyName = itemCode + "_" + typePrefix + "_TotalMoney";
            if (StrUtil.equals(currentConsultingTypeId, categoryId)) {
                Double currentValue = safeGetDouble(innerMap, keyName);
                innerMap.put(keyName, NumberUtil.add(currentValue, ObjectUtil.defaultIfNull(amount, 0.0)));
            }
        }
    }

    /**
     * 处理编制类型的统计数据
     *
     * @param innerMap
     * @param currentModel
     * @param consultingTypeMapList
     * <AUTHOR>
     * @date 2025/7/31 17:28
     */
    private void processBzType(Map<String, Object> innerMap, ThsUserBusinessStatisticsModel currentModel, List<Map<String, Object>> consultingTypeMapList) {
        // 安全处理记录数
        Integer bzRecordCount = safeGetInteger(innerMap, "BZ_RecordCount");
        if (bzRecordCount == 0) {
            bzRecordCount = ObjectUtil.defaultIfNull(currentModel.getRecordCount(), 0);
        }
        innerMap.put("BZ_RecordCount", bzRecordCount);
        // 项目数量累加
        Integer bzXMCount = safeGetInteger(innerMap, "BZ_XMCount");
        innerMap.put("BZ_XMCount", bzXMCount + 1);
        // 总金额累加
        Double bzTotalMoney = safeGetDouble(innerMap, "BZ_TotalMoney");
        Double currentTotalPrice = ObjectUtil.defaultIfNull(currentModel.getTotalPrice(), 0.0);
        innerMap.put("BZ_TotalMoney", NumberUtil.add(bzTotalMoney, currentTotalPrice));
        // 根据咨询类型累加金额
        accumulateConsultingTypeMoney(innerMap, consultingTypeMapList, currentModel.getConsultingTypeId(), "BZ", currentTotalPrice);
    }

    /**
     * 处理审核类型的统计数据
     *
     * @param innerMap
     * @param currentModel
     * @param consultingTypeMapList
     * @param shData
     * <AUTHOR>
     * @date 2025/7/31 17:28
     */
    private void processShType(Map<String, Object> innerMap, ThsUserBusinessStatisticsModel currentModel, List<Map<String, Object>> consultingTypeMapList) {
        // 记录数累加
        Integer shRecordCount = safeGetInteger(innerMap, "SH_RecordCount");
        Integer currentRecordCount = ObjectUtil.defaultIfNull(currentModel.getRecordCount(), 0);
        innerMap.put("SH_RecordCount", NumberUtil.add(shRecordCount, currentRecordCount));

        // 项目数量累加
        Integer shXMCount = safeGetInteger(innerMap, "SH_XMCount");
        innerMap.put("SH_XMCount", shXMCount + 1);

        // 总金额累加
        Double shTotalMoney = safeGetDouble(innerMap, "SH_TotalMoney");
        Double currentTotalPrice = ObjectUtil.defaultIfNull(currentModel.getTotalPrice(), 0.0);
        innerMap.put("SH_TotalMoney", NumberUtil.add(shTotalMoney, currentTotalPrice));

        // 送审金额累加
        Double shSendTotalMoney = safeGetDouble(innerMap, "SH_SendTotalMoney");
        Double currentSendTotalPrice = ObjectUtil.defaultIfNull(currentModel.getSendTotalPrice(), 0.0);
        innerMap.put("SH_SendTotalMoney", NumberUtil.add(shSendTotalMoney, currentSendTotalPrice));

        // 核减金额累加
        Double shHJMoney = safeGetDouble(innerMap, "SH_HJMoney");
        Double currentDeductionAmount = ObjectUtil.defaultIfNull(currentModel.getDeductionAmount(), 0.0);
        innerMap.put("SH_HJMoney", NumberUtil.add(shHJMoney, currentDeductionAmount));

        // 核增金额累加
        Double shHZMoney = safeGetDouble(innerMap, "SH_HZMoney");
        Double currentGrantAmount = ObjectUtil.defaultIfNull(currentModel.getGrantAmount(), 0.0);
        innerMap.put("SH_HZMoney", NumberUtil.add(shHZMoney, currentGrantAmount));

        // 根据咨询类型累加金额
        accumulateConsultingTypeMoney(innerMap, consultingTypeMapList, currentModel.getConsultingTypeId(), "SH", currentTotalPrice);
    }
}
